# Manual Base Variant Override Feature

## Overview

The product configurator endpoint now supports manual base variant override through the `baseVariantId` query parameter. This allows you to specify a particular variant to use as the base variant instead of relying on the automatic selection algorithm.

## API Endpoint

```
GET /storefront/product-configurator/{slug}?baseVariantId={variantId}
```

## Usage Examples

### 1. Automatic Base Variant Selection (Default Behavior)
```
GET /storefront/product-configurator/blackout
```
Returns the automatically selected cheapest variant as the base variant.

### 2. Manual Base Variant Override
```
GET /storefront/product-configurator/blackout?baseVariantId=123
```
Uses variant with ID 123 as the base variant (if valid).

### 3. Manual Base Variant with Option Filters
```
GET /storefront/product-configurator/blackout?baseVariantId=123&opening_operation=manual%20opening&fabric_colour=black
```
Uses variant 123 as base variant and applies option filters for remaining options.

## Validation and Fallback Behavior

The system validates the provided `baseVariantId` and falls back to automatic selection if:

1. **Variant doesn't exist**: The specified ID doesn't exist in the database
2. **Wrong product**: The variant belongs to a different product
3. **Inactive variant**: The variant status is not ACTIVE
4. **Unavailable variant**: The variant availability is not PURCHASABLE (or CONFIGURATOR_PRICING_ONLY when applicable)

### Example Fallback Scenarios

```bash
# Non-existent variant ID - falls back to automatic
GET /storefront/product-configurator/blackout?baseVariantId=999999

# Variant from different product - falls back to automatic  
GET /storefront/product-configurator/blackout?baseVariantId=456  # where 456 belongs to 'window' product

# Inactive variant - falls back to automatic
GET /storefront/product-configurator/blackout?baseVariantId=789  # where 789 is inactive
```

## Implementation Details

### New Service Method

The `BaseVariantService` now includes a new method:

```php
public function findBaseVariantWithManualOverride(
    ProductInterface $product, 
    array $selectedOptions = [], 
    ?int $baseVariantId = null, 
    bool $includeConfiguratorPricingVariants = false
): ?ProductVariantInterface
```

### State Provider Changes

The `StorefrontConfiguratorStateProvider` now:
1. Extracts `baseVariantId` from query parameters
2. Removes it from the parameters array to prevent interference with option filtering
3. Passes it to the enhanced base variant service

### Backward Compatibility

✅ **Fully backward compatible** - existing API calls without `baseVariantId` work exactly as before.

## Response Format

The response format remains unchanged. The `baseVariant` field will contain the manually specified variant (if valid) or the automatically selected variant (if invalid/fallback).

```json
{
  "variantId": null,
  "baseVariant": {
    "id": 123,
    "sku": "BPS P01 B03",
    "primaryImage": {...},
    "currencyPrices": {...}
  },
  "remainingOptions": [...]
}
```

## Testing

### Manual Testing

1. **Get available variants for a product:**
   ```bash
   # First, get the configurator without override to see available options
   curl "http://localhost:8000/storefront/product-configurator/blackout"
   ```

2. **Test manual override:**
   ```bash
   # Use a specific variant ID
   curl "http://localhost:8000/storefront/product-configurator/blackout?baseVariantId=123"
   ```

3. **Test invalid override:**
   ```bash
   # Should fall back to automatic selection
   curl "http://localhost:8000/storefront/product-configurator/blackout?baseVariantId=999999"
   ```

### Automated Tests

The feature includes comprehensive tests in:
- `tests/Integration/Api/ProductStorefrontConfiguratorManualBaseVariantTest.php`

Test scenarios covered:
- ✅ Manual override with valid variant ID
- ✅ Fallback with invalid variant ID  
- ✅ Fallback with variant from different product
- ✅ Fallback with inactive variant
- ✅ Manual override combined with option filters

## Use Cases

### 1. Product Comparison
Allow users to compare specific variants by setting a consistent base variant across different option combinations.

### 2. Promotional Pricing
Force a specific variant as the base for promotional campaigns or special pricing scenarios.

### 3. Custom Configuration Flows
Enable custom UI flows where the base variant is pre-selected based on user preferences or business logic.

### 4. A/B Testing
Test different base variants to optimize conversion rates or user experience.

## Security Considerations

- ✅ **Validation**: All variant IDs are validated before use
- ✅ **Product Isolation**: Variants from other products cannot be used
- ✅ **Status Checking**: Only active, purchasable variants are allowed
- ✅ **Graceful Fallback**: Invalid requests fall back to safe automatic selection
- ✅ **No Privilege Escalation**: Users cannot access variants they shouldn't see

## Performance Impact

- **Minimal**: Single additional database lookup only when `baseVariantId` is provided
- **Optimized**: Validation happens early with quick fallback to existing logic
- **Cached**: Leverages existing entity manager caching mechanisms
