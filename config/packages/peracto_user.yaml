peracto_user:
  entities:
    user:
      classes:
        model: App\Entity\User
  user_groups:
    classes:
      !php/const App\Component\User\UserGroup\Group\ConsumerUserGroup::IDENTIFIER: App\Component\User\UserGroup\Group\ConsumerUserGroup
      !php/const Peracto\Component\User\UserGroup\Group\GuestUserGroup::IDENTIFIER: Peracto\Component\User\UserGroup\Group\GuestUserGroup

  recaptcha:
    enabled: true
    endpoints:
      - '/users'
      - '/form-submissions'
