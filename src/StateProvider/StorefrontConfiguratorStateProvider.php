<?php

declare(strict_types=1);

namespace App\StateProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Component\Product\Configurator\BaseVariantService;
use App\Component\Product\Configurator\BaseVariantServiceInterface;
use App\Repository\ProductVariantRepositoryInterface;
use Peracto\Component\Product\Mapper\ProductMapperInterface;
use Peracto\Component\Product\Model\ProductInterface;
use Peracto\Component\Product\Repository\ProductRepositoryInterface;
use Peracto\Component\Product\Visibility\ProductVisibilityInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Contracts\Translation\TranslatorInterface;

final readonly class StorefrontConfiguratorStateProvider implements ProviderInterface
{
    public function __construct(
        private ProductRepositoryInterface $productRepository,
        private ProductVariantRepositoryInterface $productVariantRepository,
        private ProductVisibilityInterface $productVisibility,
        private ProductMapperInterface $productMapper,
        private TranslatorInterface $translator,
        private Security $security,
        private BaseVariantServiceInterface $baseVariantService,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $product = $this->productRepository->findOneBySlug($uriVariables['slug']);

        if (
            is_null($product) ||
            !$this->productVisibility->isVisible($product, ProductInterface::DISPLAY_PRODUCT_URL)
        ) {
            throw new NotFoundHttpException($this->translator->trans('peracto.catalogue_bundle.product.not_found'));
        }

        $class = $operation->getClass();

        return $this->productMapper->map($product, new $class(), $this->buildContext($product, $context));
    }

    private function buildContext(ProductInterface $product, array $context): array
    {
        $parameters = $context['filters'] ?? [];

        // Extract baseVariantId from parameters if provided
        $baseVariantId = null;
        if (isset($parameters['baseVariantId']) && is_numeric($parameters['baseVariantId'])) {
            $baseVariantId = (int) $parameters['baseVariantId'];
            // Remove baseVariantId from parameters so it doesn't interfere with option filtering
            unset($parameters['baseVariantId']);
        }

        $context[self::class]['baseVariant'] = $this->baseVariantService->findBaseVariantWithManualOverride($product, $parameters, $baseVariantId);
        $context[self::class]['variant'] = $this->productVariantRepository->findProductVariantForOptions($product, $parameters);
        $context[self::class]['parameters'] = $parameters;
        $context[self::class]['user'] = $this->security->getUser();

        return $context;
    }
}
