<?php

declare(strict_types=1);

namespace App\Component\Product\Factory;

use App\Component\Pricing\Factory\ProductPriceFactoryInterface;
use App\Component\Pricing\Model\NegativeFriendlyPrice;
use App\Component\Product\Configurator\BaseVariantServiceInterface;
use App\Entity\ProductInterface;
use App\Entity\ProductVariantInterface;
use App\Repository\ProductOptionRepositoryInterface;
use App\Repository\ProductVariantRepositoryInterface;
use Peracto\Component\Doctrine\Model\CountryInterface;
use Peracto\Component\Doctrine\Model\CurrencyInterface;
use Peracto\Component\Pricing\Model\Price;
use Peracto\Component\Pricing\Model\PriceInterface;
use Peracto\Component\Product\Model\ProductPrices;

class ProductConfiguratorPriceFactory implements ProductConfiguratorPriceFactoryInterface
{
    private const CURRENCIES_TO_INCLUDE = [
        CountryInterface::COUNTRY_GB => CurrencyInterface::CURRENCY_GBP,
        CountryInterface::COUNTRY_IE => CurrencyInterface::CURRENCY_EUR,
    ];

    public function __construct(
        private ProductPriceFactoryInterface $productPriceFactory,
        private BaseVariantServiceInterface $baseVariantService,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function generatePriceDifferences(ProductVariantInterface $baseVariant, array $remainingOptions, array $selectedOptions): array
    {
        $product = $baseVariant->getProduct();

        $priceDifferences = [];

        $configuratorPricingBaseVariant = $this->baseVariantService->findBaseVariantWithManualOverride($product, $selectedOptions, null, true);

        if (
            null !== $configuratorPricingBaseVariant
            && $configuratorPricingBaseVariant->getAvailability() === ProductInterface::AVAILABILITY_CONFIGURATOR_PRICING_ONLY
        ) {
            $baseVariant = $configuratorPricingBaseVariant;
        }

        foreach ($remainingOptions as $remainingOption) {
            $optionKey = $remainingOption->getKey();

            if (isset($selectedOptions[$optionKey])) {
                continue;
            }

            foreach ($remainingOption->getGroupedValues() as $groupedValue) {
                foreach ($groupedValue->getValues() as $value) {
                    $variantOptionIdentifier = $value->getIdentifier();

                    $newSelectedOptions = $selectedOptions;
                    $newSelectedOptions[$optionKey] = $variantOptionIdentifier;

                    $newBaseVariant = $this->baseVariantService->findBaseVariantForOptionsWithPriority($product, $newSelectedOptions, true);

                    if (null !== $newBaseVariant) {
                        $priceDifferences[$optionKey][$variantOptionIdentifier] = $this->getPriceDifferences($baseVariant, $newBaseVariant);
                    }
                }
            }
        }

        return $priceDifferences;
    }

    /**
     * @return array<string, ProductPrices>
     */
    public function getPriceDifferences(ProductVariantInterface $baseVariant, ProductVariantInterface $newBaseVariant): array
    {
        $priceDifferences = [];

        $product = $baseVariant->getProduct();

        foreach (self::CURRENCIES_TO_INCLUDE as $country => $currency) {
            $currentPrice = $baseVariant->getCurrencyPrice($currency);
            $currentSalePrice = $baseVariant->getCurrencySalePrice($currency);
            $currentClearancePrice = $baseVariant->getCurrencyClearancePrice($currency);
            $currentRrp = $baseVariant->getCurrencyRrp($currency);

            $newPrice = $newBaseVariant->getCurrencyPrice($currency);
            $newSalePrice = $newBaseVariant->getCurrencySalePrice($currency);
            $newClearancePrice = $newBaseVariant->getCurrencyClearancePrice($currency);
            $newRrp = $newBaseVariant->getCurrencyRrp($currency);

            $prices = new ProductPrices();
            $prices->setPrice($this->getTaxedPriceDifference($currentPrice, $newPrice, $product, $country));
            $prices->setSalePrice($this->getTaxedPriceDifference($currentSalePrice, $newSalePrice, $product, $country));
            $prices->setClearancePrice($this->getTaxedPriceDifference($currentClearancePrice, $newClearancePrice, $product, $country));
            $prices->setRrp($this->getTaxedPriceDifference($currentRrp, $newRrp, $product, $country));

            $priceDifferences[$currency] = $prices;
        }

        return $priceDifferences;
    }

    private function getTaxedPriceDifference(?int $currentPrice, ?int $newPrice, ProductInterface $product, ?string $country = null): PriceInterface
    {
        $currentTaxCalculatedPrice = $this->productPriceFactory->createPriceForProduct($currentPrice ?? 0, $product, $country);
        $newTaxCalculatedPrice = $this->productPriceFactory->createPriceForProduct($newPrice ?? 0, $product, $country);
        $originalDifference = $newTaxCalculatedPrice->getOriginal() - $currentTaxCalculatedPrice->getOriginal();
        $withTaxDifference = $newTaxCalculatedPrice->getIncTax() - $currentTaxCalculatedPrice->getIncTax();
        $withoutTaxDifference = $newTaxCalculatedPrice->getExcTax() - $currentTaxCalculatedPrice->getExcTax();

        return new NegativeFriendlyPrice($originalDifference, $withTaxDifference, $withoutTaxDifference);
    }
}
