<?php

declare(strict_types=1);

namespace App\Component\Product\Configurator;

use App\Entity\ProductVariantInterface;
use Peracto\Component\Product\Model\ProductInterface;

interface BaseVariantServiceInterface
{
    public function findBaseVariantForOptionsWithPriority(ProductInterface $product, array $selectedOptions = [], bool $includeConfiguratorPricingVariants = false): ?ProductVariantInterface;

    public function findBaseVariantWithManualOverride(ProductInterface $product, array $selectedOptions = [], ?int $baseVariantId = null, bool $includeConfiguratorPricingVariants = false): ?ProductVariantInterface;
}
