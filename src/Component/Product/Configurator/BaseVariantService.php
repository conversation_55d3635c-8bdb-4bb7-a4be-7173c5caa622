<?php

declare(strict_types=1);

namespace App\Component\Product\Configurator;

use App\Entity\ProductVariantInterface;
use App\Repository\ProductOptionRepositoryInterface;
use App\Repository\ProductVariantRepositoryInterface;
use Peracto\Component\Product\Model\ProductInterface;

readonly class BaseVariantService implements BaseVariantServiceInterface
{
    public function __construct(
        private ProductVariantRepositoryInterface $productVariantRepository,
        private ProductOptionRepositoryInterface $productOptionRepository,
    ) {
    }

    public function findBaseVariantForOptionsWithPriority(ProductInterface $product, array $selectedOptions = [], bool $includeConfiguratorPricingVariants = false): ?ProductVariantInterface
    {
        $productOptionsWithPriority = $this->productOptionRepository->findProductOptionsWithPrioritySet($product);

        $originalBaseVariant = $this->productVariantRepository->findBaseProductVariantForOptions($product, $selectedOptions, $includeConfiguratorPricingVariants);

        if (empty($productOptionsWithPriority)) {
            return $originalBaseVariant;
        }

        foreach ($productOptionsWithPriority as $productOption) {
            if (isset($selectedOptions[$productOption->getKey()])) {
                continue;
            }

            $selectedOptions[$productOption->getKey()] = $productOption->getPrioritisedVariantOptionIdentifier();
        }

        $prioritisedBaseVariant = $this->productVariantRepository->findBaseProductVariantForOptions($product, $selectedOptions, $includeConfiguratorPricingVariants);

        return $prioritisedBaseVariant ?? $originalBaseVariant;
    }

    public function findBaseVariantWithManualOverride(ProductInterface $product, array $selectedOptions = [], ?int $baseVariantId = null, bool $includeConfiguratorPricingVariants = false): ?ProductVariantInterface
    {
        // If no manual override is specified, use the standard priority-based selection
        if (null === $baseVariantId) {
            return $this->findBaseVariantForOptionsWithPriority($product, $selectedOptions, $includeConfiguratorPricingVariants);
        }

        // Validate that the specified variant exists and belongs to this product
        $manualBaseVariant = $this->productVariantRepository->find($baseVariantId);

        if (null === $manualBaseVariant) {
            // Variant doesn't exist, fall back to automatic selection
            return $this->findBaseVariantForOptionsWithPriority($product, $selectedOptions, $includeConfiguratorPricingVariants);
        }

        if ($manualBaseVariant->getProduct()->getId() !== $product->getId()) {
            // Variant doesn't belong to this product, fall back to automatic selection
            return $this->findBaseVariantForOptionsWithPriority($product, $selectedOptions, $includeConfiguratorPricingVariants);
        }

        // Validate variant availability
        $allowedAvailabilities = [
            \Peracto\Component\Product\Model\ProductInterface::AVAILABILITY_PURCHASABLE,
        ];

        if ($includeConfiguratorPricingVariants) {
            $allowedAvailabilities[] = \App\Entity\ProductInterface::AVAILABILITY_CONFIGURATOR_PRICING_ONLY;
        }

        if (!in_array($manualBaseVariant->getAvailability(), $allowedAvailabilities, true)) {
            // Variant is not available, fall back to automatic selection
            return $this->findBaseVariantForOptionsWithPriority($product, $selectedOptions, $includeConfiguratorPricingVariants);
        }

        // Validate variant status
        if ($manualBaseVariant->getStatus() !== \Peracto\Component\Product\Model\ProductInterface::STATUS_ACTIVE) {
            // Variant is not active, fall back to automatic selection
            return $this->findBaseVariantForOptionsWithPriority($product, $selectedOptions, $includeConfiguratorPricingVariants);
        }

        return $manualBaseVariant;
    }
}
