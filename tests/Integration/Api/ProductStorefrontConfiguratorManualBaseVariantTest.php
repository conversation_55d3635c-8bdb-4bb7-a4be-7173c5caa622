<?php

declare(strict_types=1);

namespace App\Tests\Integration\Api;

use App\Entity\ProductInterface;
use App\Entity\ProductVariantInterface;
use App\Tests\Integration\OptimizedJsonApiTestCase;
use Symfony\Component\HttpFoundation\Response;

class ProductStorefrontConfiguratorManualBaseVariantTest extends OptimizedJsonApiTestCase
{
    public function test_configurator_with_manual_base_variant_override(): void
    {
        // Get a product with multiple variants
        $product = $this->getProduct('blackout');
        self::assertNotNull($product);

        // Get all variants for this product to find a specific one to use as manual base variant
        $variants = $this->getEntityManager()
            ->getRepository(\App\Entity\ProductVariant::class)
            ->findBy([
                'product' => $product,
                'status' => ProductInterface::STATUS_ACTIVE,
                'availability' => ProductInterface::AVAILABILITY_PURCHASABLE
            ]);

        self::assertGreaterThan(1, count($variants), 'Product should have multiple variants for this test');

        // Get the second variant (not the cheapest) to use as manual base variant
        $manualBaseVariant = $variants[1];
        self::assertInstanceOf(ProductVariantInterface::class, $manualBaseVariant);

        // Test without manual override (should get automatic base variant)
        $response = $this->client->request('GET', '/storefront/product-configurator/blackout');
        $this->assertResponseCode($response, Response::HTTP_OK);

        $responseArray = $response->toArray();
        $automaticBaseVariantSku = $responseArray['baseVariant']['sku'];

        // Test with manual override
        $query = '?' . http_build_query(['baseVariantId' => $manualBaseVariant->getId()]);
        $response = $this->client->request('GET', '/storefront/product-configurator/blackout' . $query);
        $this->assertResponseCode($response, Response::HTTP_OK);

        $responseArray = $response->toArray();
        $manualBaseVariantSku = $responseArray['baseVariant']['sku'];

        // Verify that manual override worked
        self::assertSame($manualBaseVariant->getSku(), $manualBaseVariantSku);
        self::assertNotSame($automaticBaseVariantSku, $manualBaseVariantSku, 'Manual base variant should be different from automatic selection');
    }

    public function test_configurator_with_invalid_base_variant_id_falls_back_to_automatic(): void
    {
        // Test with non-existent variant ID
        $query = '?' . http_build_query(['baseVariantId' => 999999]);
        $response = $this->client->request('GET', '/storefront/product-configurator/blackout' . $query);
        $this->assertResponseCode($response, Response::HTTP_OK);

        $responseArray = $response->toArray();
        
        // Should fall back to automatic selection
        self::assertArrayHasKey('baseVariant', $responseArray);
        self::assertArrayHasKey('sku', $responseArray['baseVariant']);
    }

    public function test_configurator_with_variant_from_different_product_falls_back_to_automatic(): void
    {
        // Get a variant from a different product
        $windowProduct = $this->getProduct('window');
        self::assertNotNull($windowProduct);

        $windowVariants = $this->getEntityManager()
            ->getRepository(\App\Entity\ProductVariant::class)
            ->findBy([
                'product' => $windowProduct,
                'status' => ProductInterface::STATUS_ACTIVE,
                'availability' => ProductInterface::AVAILABILITY_PURCHASABLE
            ], null, 1);

        self::assertNotEmpty($windowVariants, 'Window product should have variants');
        $windowVariant = $windowVariants[0];

        // Try to use window variant as base variant for blackout product
        $query = '?' . http_build_query(['baseVariantId' => $windowVariant->getId()]);
        $response = $this->client->request('GET', '/storefront/product-configurator/blackout' . $query);
        $this->assertResponseCode($response, Response::HTTP_OK);

        $responseArray = $response->toArray();
        
        // Should fall back to automatic selection for blackout product
        self::assertArrayHasKey('baseVariant', $responseArray);
        self::assertNotSame($windowVariant->getSku(), $responseArray['baseVariant']['sku']);
    }

    public function test_configurator_with_inactive_variant_falls_back_to_automatic(): void
    {
        $product = $this->getProduct('blackout');
        self::assertNotNull($product);

        // Get a variant and make it inactive
        $variants = $this->getEntityManager()
            ->getRepository(\App\Entity\ProductVariant::class)
            ->findBy([
                'product' => $product,
                'status' => ProductInterface::STATUS_ACTIVE,
                'availability' => ProductInterface::AVAILABILITY_PURCHASABLE
            ]);

        self::assertGreaterThan(1, count($variants));
        $variantToDeactivate = $variants[1];
        
        // Store original status
        $originalStatus = $variantToDeactivate->getStatus();
        
        // Deactivate the variant
        $variantToDeactivate->setStatus(ProductInterface::STATUS_INACTIVE);
        $this->getEntityManager()->flush();

        try {
            // Try to use inactive variant as base variant
            $query = '?' . http_build_query(['baseVariantId' => $variantToDeactivate->getId()]);
            $response = $this->client->request('GET', '/storefront/product-configurator/blackout' . $query);
            $this->assertResponseCode($response, Response::HTTP_OK);

            $responseArray = $response->toArray();
            
            // Should fall back to automatic selection
            self::assertArrayHasKey('baseVariant', $responseArray);
            self::assertNotSame($variantToDeactivate->getSku(), $responseArray['baseVariant']['sku']);
        } finally {
            // Restore original status
            $variantToDeactivate->setStatus($originalStatus);
            $this->getEntityManager()->flush();
        }
    }

    public function test_configurator_manual_base_variant_with_options_still_works(): void
    {
        $product = $this->getProduct('blackout');
        self::assertNotNull($product);

        $variants = $this->getEntityManager()
            ->getRepository(\App\Entity\ProductVariant::class)
            ->findBy([
                'product' => $product,
                'status' => ProductInterface::STATUS_ACTIVE,
                'availability' => ProductInterface::AVAILABILITY_PURCHASABLE
            ]);

        self::assertGreaterThan(1, count($variants));
        $manualBaseVariant = $variants[1];

        // Test with both manual base variant and option filters
        $query = '?' . http_build_query([
            'baseVariantId' => $manualBaseVariant->getId(),
            'opening_operation' => 'manual opening'
        ]);
        
        $response = $this->client->request('GET', '/storefront/product-configurator/blackout' . $query);
        $this->assertResponseCode($response, Response::HTTP_OK);

        $responseArray = $response->toArray();
        
        // Should use the manual base variant
        self::assertSame($manualBaseVariant->getSku(), $responseArray['baseVariant']['sku']);
        
        // Should still process remaining options
        self::assertArrayHasKey('remainingOptions', $responseArray);
    }
}
