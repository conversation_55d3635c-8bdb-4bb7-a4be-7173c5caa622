<?php

declare(strict_types=1);

namespace App\Tests\Integration\Component;

use <PERSON>acto\Component\Recaptcha\Api\Client\RecaptchaClientInterface;
use Peracto\Component\Form\Model\FormSubmission;
use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;
use Symfony\Component\HttpFoundation\Response;

class RecaptchaTest extends OptimizedJsonApiTestCase
{
    private ?RecaptchaClientInterface $recaptchaClient;

    protected function setUp(): void
    {
        parent::setUp();

        $this->recaptchaClient = $this->getContainer()->get(MockRecaptchaClient::class);
    }

    public function test_endpoint_fails_when_token_not_sent_in_header(): void
    {
        $response = $this->client->request('POST', '/form-submissions');
        $responseArray = $response->toArray(false);

        $this->assertResponseCode($response, Response::HTTP_BAD_REQUEST);
        self::assertEquals(
            'Recaptcha token not in header: g-recaptcha-response',
            $responseArray['hydra:description'],
        );
    }

    public function test_endpoint_using_get_is_not_affected(): void
    {
        $this->authenticateClient('<EMAIL>', 'engage');
        $response = $this->client->request('GET', '/form-submissions');

        $this->assertResponseCode($response, Response::HTTP_OK);
    }

    public function test_endpoints_not_set_in_configuration_are_not_affected(): void
    {
        $response = $this->client->request('POST', '/auth', [
            'json' => [
                'email' => '<EMAIL>',
                'password' => 'engage',
            ],
        ]);

        $this->assertResponseCode($response, Response::HTTP_OK);
    }

    public function test_form_submission_fails_when_recaptcha_is_invalid(): void
    {
        $this->recaptchaClient->setResponseFile(['directory' => 'Response', 'filename' => 'failed.json']);

        self::assertEquals(1, $this->getFormSubmissionsCount());

        $response = $this->client->request('POST', '/form-submissions', [
            'json' => [
                'form' => '/forms/1',
                'values' => [
                    [
                        'formField' => '/form-fields/1',
                        'fieldValue' => 'Test User',
                    ],
                    [
                        'formField' => '/form-fields/2',
                        'fieldValue' => '<EMAIL>',
                    ],
                    [
                        'formField' => '/form-fields/3',
                        'fieldValue' => 'Contacting you to see if form submissions are working',
                    ],
                ],
            ],
            'headers' => [
                'Content-Type' => 'multipart/form-data',
                'g-recaptcha-response' => 'not-valid',
            ],
        ]);

        $this->assertResponseCode($response, Response::HTTP_BAD_REQUEST);
        self::assertEquals('Invalid reCAPTCHA', $response->toArray(false)['hydra:description']);

        self::assertEquals(1, $this->getFormSubmissionsCount());
    }

    public function test_form_submission_fails_when_recaptcha_is_missing(): void
    {
        self::assertEquals(1, $this->getFormSubmissionsCount());

        $response = $this->client->request('POST', '/form-submissions', [
            'json' => [
                'form' => '/forms/1',
                'values' => [
                    [
                        'formField' => '/form-fields/1',
                        'fieldValue' => 'Test User',
                    ],
                    [
                        'formField' => '/form-fields/2',
                        'fieldValue' => '<EMAIL>',
                    ],
                    [
                        'formField' => '/form-fields/3',
                        'fieldValue' => 'Contacting you to see if form submissions are working',
                    ],
                ],
            ],
            'headers' => [
                'Content-Type' => 'multipart/form-data',
            ],
        ]);

        $this->assertResponseCode($response, Response::HTTP_BAD_REQUEST);
        self::assertEquals(
            'Recaptcha token not in header: g-recaptcha-response',
            $response->toArray(false)['hydra:description']
        );

        self::assertEquals(1, $this->getFormSubmissionsCount());
    }

    public function test_form_submission_succeeds_when_recaptcha_is_valid(): void
    {
        $this->recaptchaClient->setResponseFile(['directory' => 'Response', 'filename' => 'successful.json']);
        self::assertEquals(1, $this->getFormSubmissionsCount());

        $response = $this->client->request('POST', '/form-submissions', [
            'json' => [
                'form' => '/forms/4',
                'values' => [
                    [
                        'formField' => '/form-fields/2',
                        'fieldValue' => '<EMAIL>',
                    ],
                    [
                        'formField' => '/form-fields/3',
                        'fieldValue' => '<EMAIL>',
                    ],
                    [
                        'formField' => '/form-fields/6',
                        'fieldValue' => 'Email | SMS',
                    ],
                    [
                        'formField' => '/form-fields/8',
                        'fieldValue' => '2021-09-28 15:08:00.446713',
                    ],
                ],
            ],
            'headers' => [
                'Content-Type' => 'multipart/form-data',
                'g-recaptcha-response' => 'valid',
            ],
        ]);

        $this->assertResponseCode($response, Response::HTTP_CREATED);

        self::assertEquals(2, $this->getFormSubmissionsCount());
    }

    public function test_form_submission_fails_when_user_is_not_admin_and_recaptcha_is_missing(): void
    {
        $response = $this->client->request('POST', '/form-submissions');

        $this->assertResponseCode($response, Response::HTTP_BAD_REQUEST);
        $this->assertJsonContains([
            "hydra:description" => "Recaptcha token not in header: g-recaptcha-response",
        ]);
    }

    public function test_form_submission_succeeds_when_user_is_admin_and_recaptcha_is_missing(): void
    {
        $this->authenticateClient('<EMAIL>', 'engage');

        $this->recaptchaClient->setResponseFile(['directory' => 'Response', 'filename' => 'successful.json']);

        $response = $this->client->request('POST', '/form-submissions', [
            'json' => [
                'form' => '/forms/4',
                'values' => [
                    [
                        'formField' => '/form-fields/2',
                        'fieldValue' => '<EMAIL>',
                    ],
                    [
                        'formField' => '/form-fields/3',
                        'fieldValue' => '<EMAIL>',
                    ],
                    [
                        'formField' => '/form-fields/6',
                        'fieldValue' => 'Email | SMS',
                    ],
                    [
                        'formField' => '/form-fields/8',
                        'fieldValue' => '2021-09-28 15:08:00.446713',
                    ],
                ],
            ],
        ]);

        $this->assertResponseCode($response, Response::HTTP_CREATED);
    }

    protected function getFormSubmissionsCount(): int
    {
        $formSubmissionRepository = $this->getEntityManager()->getRepository(FormSubmission::class);

        return count($formSubmissionRepository->findAll());
    }
}

